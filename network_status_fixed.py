"""
网络状态监控模块

该模块提供了一个网络状态检查类，用于：
- 监控交易所API连接状态
- 检查新闻API连接
- 验证AI API的可用性
- 向主应用程序发送网络状态更新信号

作者: [李兴]
版本: 1.0.0
创建日期: 2025
"""

import time
from typing import Dict, Optional, Any, Union, List, cast
import requests
import ccxt

class NetworkStatus:
    """
    网络状态监控类
    
    负责检查各种API连接的状态并发送状态更新信号。
    
    Attributes:
        exchange: 交易所API实例
        news_api_key: 新闻API密钥
        deepseek_api_key: DeepSeek AI API密钥
        network_status_signal: 网络状态更新信号
    """
    def _check_network_status_thread(self) -> None:
        """
        在后台线程中检查网络状态
        
        检查交易所API、新闻API和AI API的连接状态，
        并通过信号发送状态更新
        """
        check_results: Dict[str, bool] = {}
        
        # 检查交易所API连接
        try:
            for attempt in range(3):  # 最多尝试3次
                try:
                    status = self.exchange.fetch_status()
                    if status.get('status') == 'ok':
                        check_results["exchange"] = True
                        break
                    else:
                        time.sleep(1)
                except Exception:
                    if attempt < 2:  # 如果不是最后一次尝试
                        time.sleep(1)
                    else:
                        check_results["exchange"] = False
            
            if "exchange" not in check_results:
                check_results["exchange"] = False
                
        except Exception:
            check_results["exchange"] = False
            
        # 检查News API连接
        if self.news_api_key:
            try:
                for attempt in range(3):  # 最多尝试3次
                    try:
                        url = 'https://newsapi.org/v2/everything'
                        params = {
                            'q': 'bitcoin',
                            'pageSize': 1,
                            'apiKey': self.news_api_key
                        }
                        response = requests.get(url, params=params, timeout=(10, 30))  # (连接超时, 读取超时)
                        if response.status_code == 200:
                            check_results["news_api"] = True
                            break
                        else:
                            time.sleep(1)
                    except Exception:
                        if attempt < 2:  # 如果不是最后一次尝试
                            time.sleep(1)
                        else:
                            check_results["news_api"] = False
                
                if "news_api" not in check_results:
                    check_results["news_api"] = False
            except Exception:
                check_results["news_api"] = False
        else:
            check_results["news_api"] = False
            
        # 检查DeepSeek AI API连接
        if self.deepseek_api_key:
            try:
                for attempt in range(3):  # 最多尝试3次
                    try:
                        headers = {
                            'Authorization': f'Bearer {self.deepseek_api_key}',
                            'Content-Type': 'application/json'
                        }
                        api_url = "https://api.deepseek.com/v1/chat/completions"
                        api_data = {
                            "model": "deepseek-chat",
                            "messages": [{"role": "user", "content": "Hi"}],
                            "max_tokens": 5
                        }
                        response = requests.post(api_url, json=api_data, headers=headers, timeout=5)
                        if response.status_code == 200:
                            check_results["ai_api"] = True
                            break
                        else:
                            time.sleep(1)
                    except Exception:
                        if attempt < 2:  # 如果不是最后一次尝试
                            time.sleep(1)
                        else:
                            check_results["ai_api"] = False
                
                if "ai_api" not in check_results:
                    check_results["ai_api"] = False
            except Exception:
                check_results["ai_api"] = False
        else:
            check_results["ai_api"] = False
        
        # 更新所有状态
        for api_name, is_connected in check_results.items():
            self.network_status_signal.emit(api_name, is_connected) 