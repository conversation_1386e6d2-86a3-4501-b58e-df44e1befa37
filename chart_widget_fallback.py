"""
K线图表组件模块 - 回退版本
当PyQt6-Charts不可用时使用的简化版本
"""

from typing import Dict, List, Optional, Any, Union, Tuple
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QComboBox, QLabel, QTextEdit
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor
import pandas as pd

class ChartWidget(QWidget):
    """
    K线图表组件类 - 回退版本
    当PyQt6-Charts不可用时，显示文本形式的数据
    """
    
    timeframe_changed = pyqtSignal()
    
    def __init__(self, parent: Optional[QWidget] = None) -> None:
        super().__init__(parent)
        self.current_adx: Optional[float] = None
        self.init_ui()
        
    def init_ui(self) -> None:
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)
        control_layout.setContentsMargins(0, 0, 0, 0)
        
        # 时间周期选择
        timeframe_label = QLabel("时间周期:")
        timeframe_label.setStyleSheet("color: #F8FAFC; font-weight: 600;")
        
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(['5分钟', '15分钟', '1小时', '4小时', '1天'])
        self.timeframe_combo.setCurrentText('15分钟')
        self.timeframe_combo.currentTextChanged.connect(self.on_timeframe_changed)
        
        # 指标选择
        indicator_label = QLabel("技术指标:")
        indicator_label.setStyleSheet("color: #F8FAFC; font-weight: 600;")
        
        self.indicator_combo = QComboBox()
        self.indicator_combo.addItems(['无', 'MA', 'MACD', 'RSI', 'BOLL', 'ADX'])
        
        control_layout.addWidget(timeframe_label)
        control_layout.addWidget(self.timeframe_combo)
        control_layout.addWidget(indicator_label)
        control_layout.addWidget(self.indicator_combo)
        control_layout.addStretch()
        
        layout.addWidget(control_panel)
        
        # 图表显示区域（使用文本显示）
        self.chart_display = QTextEdit()
        self.chart_display.setReadOnly(True)
        self.chart_display.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 0.95), stop:1 rgba(15, 23, 42, 0.9));
                color: #F8FAFC;
                border: 2px solid rgba(51, 65, 85, 0.8);
                border-radius: 12px;
                padding: 16px;
                font-family: 'Monaco', 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.5;
            }
        """)
        
        # 显示提示信息
        self.chart_display.setHtml("""
        <div style="text-align: center; padding: 50px; color: #94A3B8;">
            <h3 style="color: #F59E0B;">📊 K线图表 - 简化模式</h3>
            <p>PyQt6-Charts组件暂时不可用</p>
            <p>K线数据将以文本形式显示</p>
            <br>
            <p style="font-size: 11px; color: #64748B;">
                要启用完整图表功能，请修复PyQt6-Charts安装问题
            </p>
        </div>
        """)
        
        layout.addWidget(self.chart_display)
        
    def on_timeframe_changed(self):
        """时间周期改变时的处理"""
        self.timeframe_changed.emit()
        
    def update_chart(self, ohlcv_data: List[List], symbol: str = ""):
        """更新图表数据 - 文本显示版本"""
        try:
            if not ohlcv_data:
                self.chart_display.setHtml("""
                <div style="text-align: center; padding: 50px; color: #EF4444;">
                    <h3>❌ 无数据</h3>
                    <p>未能获取K线数据</p>
                </div>
                """)
                return
                
            # 转换数据为DataFrame
            df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # 计算基本统计信息
            latest = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else latest
            
            change = latest['close'] - prev['close']
            change_pct = (change / prev['close']) * 100 if prev['close'] != 0 else 0
            
            # 生成HTML显示
            html_content = f"""
            <div style="padding: 20px;">
                <h3 style="color: #3B82F6; text-align: center;">📈 {symbol} K线数据</h3>
                
                <div style="background: rgba(59, 130, 246, 0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h4 style="color: #60A5FA; margin: 0 0 10px 0;">💰 最新价格信息</h4>
                    <p><strong>开盘价:</strong> ${latest['open']:.4f}</p>
                    <p><strong>最高价:</strong> ${latest['high']:.4f}</p>
                    <p><strong>最低价:</strong> ${latest['low']:.4f}</p>
                    <p><strong>收盘价:</strong> ${latest['close']:.4f}</p>
                    <p><strong>成交量:</strong> {latest['volume']:,.0f}</p>
                    <p><strong>涨跌:</strong> <span style="color: {'#10B981' if change >= 0 else '#EF4444'}">${change:+.4f} ({change_pct:+.2f}%)</span></p>
                </div>
                
                <div style="background: rgba(16, 185, 129, 0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h4 style="color: #34D399; margin: 0 0 10px 0;">📊 统计信息</h4>
                    <p><strong>数据点数:</strong> {len(df)} 个</p>
                    <p><strong>时间范围:</strong> {df['timestamp'].iloc[0].strftime('%Y-%m-%d %H:%M')} 至 {df['timestamp'].iloc[-1].strftime('%Y-%m-%d %H:%M')}</p>
                    <p><strong>平均价格:</strong> ${df['close'].mean():.4f}</p>
                    <p><strong>价格波动:</strong> ${df['close'].std():.4f}</p>
                </div>
                
                <div style="background: rgba(245, 158, 11, 0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h4 style="color: #FBBF24; margin: 0 0 10px 0;">⚠️ 提示</h4>
                    <p>当前为简化显示模式，要查看完整的K线图表，请修复PyQt6-Charts安装问题。</p>
                    <p>运行以下命令修复：<code>python fix_pyqt6.py</code></p>
                </div>
            </div>
            """
            
            self.chart_display.setHtml(html_content)
            
        except Exception as e:
            error_html = f"""
            <div style="text-align: center; padding: 50px; color: #EF4444;">
                <h3>❌ 数据处理错误</h3>
                <p>{str(e)}</p>
            </div>
            """
            self.chart_display.setHtml(error_html)
            
    def update_adx_display(self, adx_value: float, plus_di: float, minus_di: float):
        """更新ADX显示"""
        self.current_adx = adx_value
        # 在简化模式下，ADX信息会包含在主数据显示中
        
    def set_theme(self, theme: str):
        """设置主题"""
        # 简化模式下主题设置较为有限
        pass
        
    def add_indicator(self, indicator_type: str, **kwargs):
        """添加技术指标"""
        # 简化模式下暂不支持指标显示
        pass
