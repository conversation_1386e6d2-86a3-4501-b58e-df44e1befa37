#!/usr/bin/env python3
"""
测试主要导入和基本功能
"""

import sys
import os

def test_imports():
    """测试主要模块导入"""
    print("测试导入...")
    
    try:
        # 测试PyQt6导入
        from PyQt6.QtWidgets import QApplication, QMainWindow
        from PyQt6.QtCore import Qt, QTimer, pyqtSignal
        from PyQt6.QtGui import QFont, QColor
        print("✅ PyQt6导入成功")
    except ImportError as e:
        print(f"❌ PyQt6导入失败: {e}")
        return False
    
    try:
        # 测试第三方库导入
        import ccxt
        import pandas as pd
        import talib
        import requests
        from dotenv import load_dotenv
        print("✅ 第三方库导入成功")
    except ImportError as e:
        print(f"❌ 第三方库导入失败: {e}")
        return False
    
    try:
        # 测试图表组件导入
        try:
            from chart_widget import ChartWidget
            print("✅ chart_widget导入成功")
        except ImportError:
            from chart_widget_fallback import ChartWidget
            print("✅ chart_widget_fallback导入成功")
    except ImportError as e:
        print(f"❌ 图表组件导入失败: {e}")
        return False
    
    try:
        # 测试主窗口导入
        from main_window import MainWindow
        print("✅ MainWindow导入成功")
    except ImportError as e:
        print(f"❌ MainWindow导入失败: {e}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")

    try:
        # 测试类定义和方法存在性
        from main_window import MainWindow, EnhancedTradingLog, Worker, WorkerSignals
        print("✅ 主要类导入成功")

        # 测试类是否有必要的方法
        required_methods = [
            'log_trading', 'update_tp_sl_display', 'update_market_data',
            'on_symbol_changed', 'show_message_box', 'load_config',
            'check_network_status', 'animate_background'
        ]

        for method in required_methods:
            if hasattr(MainWindow, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 缺失")
                return False

        # 测试EnhancedTradingLog类
        if hasattr(EnhancedTradingLog, 'add_log'):
            print("✅ EnhancedTradingLog.add_log 方法存在")
        else:
            print("❌ EnhancedTradingLog.add_log 方法缺失")
            return False

        return True

    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试币安量化交易机器人...")
    
    # 测试导入
    if not test_imports():
        print("❌ 导入测试失败，请检查依赖")
        sys.exit(1)
    
    # 测试基本功能
    if not test_basic_functionality():
        print("❌ 基本功能测试失败")
        sys.exit(1)
    
    print("\n✅ 所有测试通过！程序应该可以正常运行。")
