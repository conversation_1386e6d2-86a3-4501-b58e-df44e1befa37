#!/usr/bin/env python3
"""
修复快速刷新账户数据问题的脚本
"""

import os
import sys
import time
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

def test_main_window_quick_refresh():
    """测试主窗口的快速刷新功能"""
    print("🚀 测试主窗口快速刷新功能...")

    try:
        # 测试导入
        print("🔄 测试模块导入...")
        from main_window import MainWindow, Worker, WorkerSignals
        print("✅ 模块导入成功")

        # 检查类定义
        print("🔄 检查类定义...")
        required_methods = [
            'quick_refresh_account', '_quick_fetch_account_data',
            '_process_quick_account_data', '_handle_quick_refresh_error',
            '_account_update_finished'
        ]

        missing_methods = []
        for method in required_methods:
            if not hasattr(MainWindow, method):
                missing_methods.append(method)

        if missing_methods:
            print(f"❌ MainWindow缺少必要方法: {missing_methods}")
            return False

        print("✅ 所有必要方法都存在")

        # 检查Worker类
        if not hasattr(Worker, 'run'):
            print("❌ Worker类缺少run方法")
            return False

        print("✅ Worker类定义正确")

        # 检查WorkerSignals类
        required_signals = ['result', 'error', 'finished']
        for signal in required_signals:
            if not hasattr(WorkerSignals, signal):
                print(f"❌ WorkerSignals缺少信号: {signal}")
                return False

        print("✅ WorkerSignals类定义正确")

        # 测试API连接（不创建GUI）
        print("🔄 测试API连接...")
        import os
        import ccxt
        from dotenv import load_dotenv

        load_dotenv()
        api_key = os.getenv('BINANCE_API_KEY')
        secret_key = os.getenv('BINANCE_SECRET_KEY')

        if not api_key or not secret_key:
            print("❌ API密钥未配置")
            return False

        try:
            exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',
                    'adjustForTimeDifference': True,
                    'recvWindow': 10000
                },
                'timeout': 15000
            })

            # 测试连接
            status = exchange.fetch_status()
            if status.get('status') == 'ok':
                print("✅ API连接正常")
            else:
                print("⚠️  API连接状态异常")

        except Exception as e:
            print(f"❌ API连接失败: {str(e)}")
            return False

        print("✅ 所有基础测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_debug_version():
    """创建调试版本的快速刷新方法"""
    print("🔧 创建调试版本...")
    
    debug_code = '''
def debug_quick_refresh_account(self):
    """调试版本的快速刷新账户信息"""
    print("🐛 [DEBUG] 开始快速刷新...")
    
    try:
        # 详细的状态检查
        print(f"🐛 [DEBUG] is_account_updating: {getattr(self, 'is_account_updating', 'NOT_SET')}")
        print(f"🐛 [DEBUG] exchange: {hasattr(self, 'exchange') and self.exchange is not None}")
        print(f"🐛 [DEBUG] thread_pool: {hasattr(self, 'thread_pool') and self.thread_pool is not None}")
        
        # 防止重复更新
        if hasattr(self, 'is_account_updating') and self.is_account_updating:
            print("🐛 [DEBUG] 账户更新正在进行中，跳过本次请求")
            return

        # 检查更新时间间隔
        current_time = time.time()
        if hasattr(self, 'last_account_update_time') and hasattr(self, 'account_update_interval'):
            time_diff = current_time - self.last_account_update_time
            print(f"🐛 [DEBUG] 时间间隔: {time_diff:.2f}秒, 要求间隔: {self.account_update_interval}秒")
            if time_diff < self.account_update_interval:
                print("🐛 [DEBUG] 更新间隔未到，跳过本次请求")
                return

        # 检查API连接状态
        if not hasattr(self, 'exchange') or not self.exchange:
            print("🐛 [DEBUG] 交易所连接未初始化")
            self.log_trading("交易所连接未初始化", level='error')
            return

        print("🐛 [DEBUG] 设置更新状态...")
        self.is_account_updating = True
        self.last_account_update_time = current_time

        # 更新状态指示器
        if hasattr(self, 'account_status_indicator'):
            print("🐛 [DEBUG] 更新状态指示器...")
            self.account_status_indicator.setStyleSheet("color: #F59E0B; font-size: 14px; font-weight: bold;")
            self.account_status_indicator.setToolTip("正在更新账户数据...")

        # 检查缓存
        cache_key = "account_data"
        if hasattr(self, '_account_data_cache') and cache_key in self._account_data_cache:
            cached_data = self._account_data_cache[cache_key]
            if cached_data.is_valid(30):
                print("🐛 [DEBUG] 使用缓存的账户数据")
                self.log_trading("使用缓存的账户数据", level='debug')
                self._process_quick_account_data(cached_data.data)
                self.is_account_updating = False
                if hasattr(self, 'account_status_indicator'):
                    self.account_status_indicator.setStyleSheet("color: #10B981; font-size: 14px; font-weight: bold;")
                    self.account_status_indicator.setToolTip("账户数据正常")
                return

        # 检查线程池状态
        if not hasattr(self, 'thread_pool') or not self.thread_pool:
            print("🐛 [DEBUG] 线程池未初始化")
            self.log_trading("线程池未初始化", level='error')
            self.is_account_updating = False
            return

        # 使用线程池获取账户数据
        print("🐛 [DEBUG] 启动工作线程...")
        self.log_trading("开始获取账户数据...", level='debug')
        
        from main_window import Worker
        worker = Worker(self._quick_fetch_account_data)
        worker.signals.result.connect(self._process_quick_account_data)
        worker.signals.error.connect(self._handle_quick_refresh_error)
        worker.signals.finished.connect(self._account_update_finished)
        self.thread_pool.start(worker)
        
        print("🐛 [DEBUG] 工作线程已启动")

    except Exception as e:
        print(f"🐛 [DEBUG] 启动快速刷新失败: {str(e)}")
        self.log_trading(f"启动快速刷新失败: {str(e)}", level='error')
        if hasattr(self, 'is_account_updating'):
            self.is_account_updating = False
        if hasattr(self, 'account_status_indicator'):
            self.account_status_indicator.setStyleSheet("color: #EF4444; font-size: 14px; font-weight: bold;")
            self.account_status_indicator.setToolTip(f"启动刷新失败: {str(e)}")
'''
    
    # 保存调试代码到文件
    with open('debug_quick_refresh.py', 'w', encoding='utf-8') as f:
        f.write(debug_code)
    
    print("✅ 调试版本已创建: debug_quick_refresh.py")
    print("💡 使用方法：")
    print("   1. 在主程序中导入: from debug_quick_refresh import debug_quick_refresh_account")
    print("   2. 替换原方法: window.quick_refresh_account = debug_quick_refresh_account.__get__(window)")
    print("   3. 运行程序查看详细调试信息")

def provide_solutions():
    """提供解决方案"""
    print("\n💡 快速刷新失败的可能解决方案：")
    print()
    print("1. 🔧 API连接问题：")
    print("   - 检查.env文件中的API密钥是否正确")
    print("   - 确保API密钥具有期货交易权限")
    print("   - 检查网络连接状态")
    print()
    print("2. ⏱️ 超时问题：")
    print("   - 增加API调用超时时间")
    print("   - 优化网络环境")
    print("   - 减少并发请求数量")
    print()
    print("3. 🧵 线程问题：")
    print("   - 检查线程池配置")
    print("   - 确保信号连接正确")
    print("   - 避免UI线程阻塞")
    print()
    print("4. 💾 缓存问题：")
    print("   - 清理过期缓存")
    print("   - 检查缓存有效性")
    print("   - 重置缓存状态")
    print()
    print("5. 🔄 状态管理问题：")
    print("   - 重置更新状态标志")
    print("   - 检查时间间隔设置")
    print("   - 确保状态同步")

def main():
    """主函数"""
    print("🚀 快速刷新问题修复工具")
    print("=" * 50)
    
    # 测试主窗口快速刷新
    if test_main_window_quick_refresh():
        print("\n✅ 快速刷新功能测试通过！")
    else:
        print("\n❌ 快速刷新功能测试失败")
        
        # 创建调试版本
        create_debug_version()
        
        # 提供解决方案
        provide_solutions()

if __name__ == "__main__":
    main()
