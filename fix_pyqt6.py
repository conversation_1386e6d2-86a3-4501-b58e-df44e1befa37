#!/usr/bin/env python3
"""
PyQt6修复脚本
用于解决PyQt6依赖库冲突问题
"""

import subprocess
import sys
import os

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def fix_pyqt6():
    """修复PyQt6安装问题"""
    print("🔧 开始修复PyQt6安装问题...")

    # 检查当前环境
    print("\n🔍 步骤0: 检查当前环境...")
    success, stdout, stderr = run_command("python --version")
    if success:
        print(f"Python版本: {stdout.strip()}")

    success, stdout, stderr = run_command("which python")
    if success:
        print(f"Python路径: {stdout.strip()}")

    # 1. 卸载现有的PyQt6
    print("\n📦 步骤1: 卸载现有PyQt6...")
    commands = [
        "pip uninstall PyQt6 -y",
        "pip uninstall PyQt6-Qt6 -y",
        "pip uninstall PyQt6-sip -y",
        "pip uninstall PyQt6-Charts -y"
    ]

    for cmd in commands:
        print(f"执行: {cmd}")
        success, stdout, stderr = run_command(cmd)
        if not success:
            print(f"⚠️  警告: {cmd} 执行失败: {stderr}")

    # 2. 清理缓存
    print("\n🧹 步骤2: 清理pip缓存...")
    run_command("pip cache purge")

    # 3. 检查conda环境
    print("\n🐍 步骤3: 检查conda环境...")
    success, stdout, stderr = run_command("conda --version")
    if success:
        print(f"Conda版本: {stdout.strip()}")
        print("尝试使用conda安装PyQt6...")

        conda_commands = [
            "conda install -c conda-forge pyqt -y",
            "pip install --no-cache-dir PyQt6-Charts"
        ]

        for cmd in conda_commands:
            print(f"执行: {cmd}")
            success, stdout, stderr = run_command(cmd)
            if success:
                print(f"✅ {cmd} 安装成功")
            else:
                print(f"⚠️  {cmd} 安装失败: {stderr}")
    else:
        # 4. 使用pip重新安装PyQt6
        print("\n📥 步骤4: 使用pip重新安装PyQt6...")
        install_commands = [
            "pip install --no-cache-dir --upgrade pip",
            "pip install --no-cache-dir PyQt6",
            "pip install --no-cache-dir PyQt6-Charts"
        ]

        for cmd in install_commands:
            print(f"执行: {cmd}")
            success, stdout, stderr = run_command(cmd)
            if success:
                print(f"✅ {cmd} 安装成功")
            else:
                print(f"❌ {cmd} 安装失败: {stderr}")

    # 5. 验证安装
    print("\n🔍 步骤5: 验证安装...")
    test_code = """
import sys
try:
    from PyQt6.QtWidgets import QApplication
    print("✅ PyQt6.QtWidgets 导入成功")
    try:
        from PyQt6.QtCharts import QChart, QChartView
        print("✅ PyQt6.QtCharts 导入成功")
    except ImportError as e:
        print(f"⚠️  PyQt6.QtCharts 导入失败: {e}")
        print("程序将使用回退模式运行")
except ImportError as e:
    print(f"❌ PyQt6基础组件导入失败: {e}")
    sys.exit(1)
"""

    success, stdout, stderr = run_command(f'python -c "{test_code}"')
    print(stdout)
    if stderr:
        print(f"错误信息: {stderr}")

    # 6. 测试GUI应用
    print("\n🖥️  步骤6: 测试GUI应用...")
    gui_test_code = '''
import sys
import os
os.environ["QT_QPA_PLATFORM"] = "offscreen"  # 无头模式测试
try:
    from PyQt6.QtWidgets import QApplication, QWidget
    app = QApplication(sys.argv)
    widget = QWidget()
    print("✅ GUI测试成功")
    app.quit()
except Exception as e:
    print(f"❌ GUI测试失败: {e}")
'''

    success, stdout, stderr = run_command(f'python -c "{gui_test_code}"')
    print(stdout)
    if stderr:
        print(f"GUI测试错误: {stderr}")

    print("\n🎉 PyQt6修复流程完成！")
    print("\n📋 下一步操作:")
    print("1. 如果仍有问题，请尝试重启终端")
    print("2. 运行 'python main_window.py' 测试程序")
    print("3. 程序会自动使用可用的组件运行")

    return True

if __name__ == "__main__":
    fix_pyqt6()
