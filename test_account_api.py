#!/usr/bin/env python3
"""
测试币安API连接和账户数据获取
"""

import os
import sys
import ccxt
import time
import concurrent.futures
from dotenv import load_dotenv

def test_api_connection():
    """测试API连接"""
    print("🔍 测试API连接...")
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('BINANCE_API_KEY')
    secret_key = os.getenv('BINANCE_SECRET_KEY')
    
    if not api_key or not secret_key:
        print("❌ 未找到API密钥，请检查.env文件")
        return False
    
    print(f"✅ API密钥已加载: {api_key[:10]}...")
    print(f"✅ Secret密钥已加载: {secret_key[:10]}...")
    
    try:
        # 创建交易所连接
        exchange = ccxt.binance({
            'apiKey': api_key,
            'secret': secret_key,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'future',  # 期货交易
                'adjustForTimeDifference': True,
                'recvWindow': 10000
            },
            'timeout': 15000  # 15秒超时
        })
        
        print("✅ 交易所对象创建成功")
        
        # 测试基本连接
        print("🔄 测试交易所状态...")
        status = exchange.fetch_status()
        print(f"✅ 交易所状态: {status.get('status', 'unknown')}")
        
        return exchange
        
    except Exception as e:
        print(f"❌ API连接失败: {str(e)}")
        return False

def test_account_data(exchange):
    """测试账户数据获取"""
    print("\n💰 测试账户数据获取...")
    
    try:
        # 测试余额获取
        print("🔄 获取账户余额...")
        balance = exchange.fetch_balance()
        
        if balance:
            print("✅ 余额获取成功")
            
            # 显示主要资产
            total_usdt = balance.get('USDT', {}).get('total', 0)
            free_usdt = balance.get('USDT', {}).get('free', 0)
            used_usdt = balance.get('USDT', {}).get('used', 0)
            
            print(f"  💵 USDT总额: {total_usdt:.2f}")
            print(f"  💵 USDT可用: {free_usdt:.2f}")
            print(f"  💵 USDT已用: {used_usdt:.2f}")
            
            # 显示其他有余额的资产
            other_assets = []
            for asset, info in balance.items():
                if isinstance(info, dict) and info.get('total', 0) > 0 and asset != 'USDT':
                    other_assets.append(f"{asset}: {info['total']:.6f}")
            
            if other_assets:
                print(f"  📊 其他资产: {', '.join(other_assets[:5])}")
            
        else:
            print("❌ 余额数据为空")
            return False
            
    except Exception as e:
        print(f"❌ 获取余额失败: {str(e)}")
        return False
    
    try:
        # 测试持仓获取
        print("🔄 获取持仓信息...")
        positions = exchange.fetch_positions()
        
        if positions:
            print(f"✅ 持仓获取成功，共 {len(positions)} 个持仓")
            
            # 显示有持仓的合约
            active_positions = [p for p in positions if p.get('size', 0) != 0]
            if active_positions:
                print(f"  📈 活跃持仓: {len(active_positions)} 个")
                for pos in active_positions[:3]:  # 显示前3个
                    symbol = pos.get('symbol', 'Unknown')
                    size = pos.get('size', 0)
                    side = pos.get('side', 'unknown')
                    print(f"    - {symbol}: {side} {size}")
            else:
                print("  📊 当前无活跃持仓")
        else:
            print("❌ 持仓数据为空")
            
    except Exception as e:
        print(f"❌ 获取持仓失败: {str(e)}")
        return False
    
    return True

def test_concurrent_fetch(exchange):
    """测试并发获取数据"""
    print("\n⚡ 测试并发数据获取...")
    
    try:
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            # 提交并行任务
            balance_future = executor.submit(exchange.fetch_balance)
            positions_future = executor.submit(exchange.fetch_positions)
            
            # 获取结果
            try:
                balance = balance_future.result(timeout=8)
                print("✅ 并发获取余额成功")
            except concurrent.futures.TimeoutError:
                print("⏰ 并发获取余额超时")
                return False
            except Exception as e:
                print(f"❌ 并发获取余额失败: {str(e)}")
                return False
            
            try:
                positions = positions_future.result(timeout=8)
                print("✅ 并发获取持仓成功")
            except concurrent.futures.TimeoutError:
                print("⏰ 并发获取持仓超时")
                return False
            except Exception as e:
                print(f"❌ 并发获取持仓失败: {str(e)}")
                return False
        
        end_time = time.time()
        print(f"✅ 并发获取完成，耗时: {end_time - start_time:.2f}秒")
        return True
        
    except Exception as e:
        print(f"❌ 并发获取失败: {str(e)}")
        return False

def test_network_conditions():
    """测试网络条件"""
    print("\n🌐 测试网络条件...")
    
    try:
        import requests
        
        # 测试币安API连通性
        print("🔄 测试币安API连通性...")
        response = requests.get('https://fapi.binance.com/fapi/v1/ping', timeout=5)
        if response.status_code == 200:
            print("✅ 币安期货API连通正常")
        else:
            print(f"❌ 币安期货API连通异常: {response.status_code}")
            
        # 测试延迟
        start_time = time.time()
        response = requests.get('https://fapi.binance.com/fapi/v1/time', timeout=5)
        end_time = time.time()
        
        if response.status_code == 200:
            latency = (end_time - start_time) * 1000
            print(f"✅ API延迟: {latency:.0f}ms")
            
            # 检查时间同步
            server_time = response.json()['serverTime']
            local_time = int(time.time() * 1000)
            time_diff = abs(server_time - local_time)
            
            print(f"⏰ 时间差: {time_diff}ms")
            if time_diff > 5000:
                print("⚠️  时间差过大，可能影响API调用")
            else:
                print("✅ 时间同步正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 网络测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始币安API诊断测试...\n")
    
    # 测试网络条件
    if not test_network_conditions():
        print("\n❌ 网络条件测试失败，请检查网络连接")
        return
    
    # 测试API连接
    exchange = test_api_connection()
    if not exchange:
        print("\n❌ API连接测试失败，请检查API密钥配置")
        return
    
    # 测试账户数据
    if not test_account_data(exchange):
        print("\n❌ 账户数据测试失败")
        return
    
    # 测试并发获取
    if not test_concurrent_fetch(exchange):
        print("\n❌ 并发获取测试失败")
        return
    
    print("\n✅ 所有测试通过！API连接和账户数据获取正常。")
    print("\n💡 如果主程序仍然出现问题，可能的原因：")
    print("   1. 程序运行时的网络环境变化")
    print("   2. API调用频率限制")
    print("   3. 程序内部的线程同步问题")
    print("   4. 缓存或状态管理问题")

if __name__ == "__main__":
    main()
