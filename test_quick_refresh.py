#!/usr/bin/env python3
"""
测试快速刷新账户数据功能
"""

import os
import sys
import ccxt
import time
import concurrent.futures
from dotenv import load_dotenv

class QuickRefreshTester:
    """快速刷新测试器"""
    
    def __init__(self):
        # 加载环境变量
        load_dotenv()
        self.api_key = os.getenv('BINANCE_API_KEY')
        self.secret_key = os.getenv('BINANCE_SECRET_KEY')
        
        # 初始化交易所连接
        self.exchange = ccxt.binance({
            'apiKey': self.api_key,
            'secret': self.secret_key,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'future',
                'adjustForTimeDifference': True,
                'recvWindow': 10000
            },
            'timeout': 15000
        })
        
        # 初始化状态变量
        self.is_account_updating = False
        self.last_account_update_time = 0
        self.account_update_interval = 2.0
    
    def _quick_fetch_account_data(self):
        """快速获取账户数据（模拟主程序逻辑）"""
        try:
            print("🔄 开始并发获取账户数据...")
            
            # 检查交易所连接状态
            if not self.exchange:
                raise Exception("交易所连接未初始化")

            # 并行获取账户相关数据
            with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                balance_future = executor.submit(self.exchange.fetch_balance)
                positions_future = executor.submit(self.exchange.fetch_positions)

                # 获取结果，增加超时时间并处理具体异常
                balance = None
                positions = None
                
                try:
                    balance = balance_future.result(timeout=8)
                    print("✅ 余额获取成功")
                except concurrent.futures.TimeoutError:
                    raise Exception("获取账户余额超时，请检查网络连接")
                except Exception as e:
                    if "authentication" in str(e).lower():
                        raise Exception("API认证失败，请检查API密钥配置")
                    elif "permission" in str(e).lower():
                        raise Exception("API权限不足，请检查API权限设置")
                    elif "network" in str(e).lower() or "timeout" in str(e).lower():
                        raise Exception("网络连接问题，请检查网络状态")
                    else:
                        raise Exception(f"获取余额失败: {str(e)}")
                
                try:
                    positions = positions_future.result(timeout=8)
                    print("✅ 持仓获取成功")
                except concurrent.futures.TimeoutError:
                    raise Exception("获取持仓信息超时，请检查网络连接")
                except Exception as e:
                    if "authentication" in str(e).lower():
                        raise Exception("API认证失败，请检查API密钥配置")
                    elif "permission" in str(e).lower():
                        raise Exception("API权限不足，请检查API权限设置")
                    elif "network" in str(e).lower() or "timeout" in str(e).lower():
                        raise Exception("网络连接问题，请检查网络状态")
                    else:
                        raise Exception(f"获取持仓失败: {str(e)}")

                return {
                    'balance': balance,
                    'positions': positions
                }

        except Exception as e:
            # 记录详细错误信息
            error_msg = str(e)
            if "快速获取账户数据失败" not in error_msg:
                error_msg = f"快速获取账户数据失败: {error_msg}"
            raise Exception(error_msg)
    
    def quick_refresh_account(self):
        """快速刷新账户信息（模拟主程序逻辑）"""
        try:
            print("\n🚀 开始快速刷新账户信息...")
            
            # 防止重复更新
            if self.is_account_updating:
                print("⏸️  账户更新正在进行中，跳过本次请求")
                return False

            # 检查更新时间间隔
            current_time = time.time()
            if current_time - self.last_account_update_time < self.account_update_interval:
                print("⏸️  更新间隔未到，跳过本次请求")
                return False

            # 检查API连接状态
            if not self.exchange:
                print("❌ 交易所连接未初始化")
                return False

            self.is_account_updating = True
            self.last_account_update_time = current_time

            print("🔄 正在更新账户数据...")

            # 获取账户数据
            start_time = time.time()
            data = self._quick_fetch_account_data()
            end_time = time.time()
            
            # 处理数据
            self._process_quick_account_data(data)
            
            print(f"✅ 快速刷新完成，耗时: {end_time - start_time:.2f}秒")
            return True

        except Exception as e:
            print(f"❌ 快速刷新失败: {str(e)}")
            return False
        finally:
            self.is_account_updating = False
    
    def _process_quick_account_data(self, data):
        """处理快速获取的账户数据"""
        try:
            print("📊 处理账户数据...")
            
            if data.get('balance'):
                balance = data['balance']
                usdt_total = balance.get('USDT', {}).get('total', 0)
                usdt_free = balance.get('USDT', {}).get('free', 0)
                print(f"  💰 USDT总额: {usdt_total:.2f}")
                print(f"  💰 USDT可用: {usdt_free:.2f}")

            if data.get('positions'):
                positions = data['positions']
                active_positions = [p for p in positions if p.get('size', 0) != 0]
                print(f"  📈 活跃持仓: {len(active_positions)} 个")
                
                for pos in active_positions[:3]:  # 显示前3个
                    symbol = pos.get('symbol', 'Unknown')
                    size = pos.get('size', 0)
                    side = pos.get('side', 'unknown')
                    print(f"    - {symbol}: {side} {size}")

        except Exception as e:
            print(f"❌ 处理账户数据失败: {str(e)}")

def test_multiple_refreshes():
    """测试多次快速刷新"""
    print("🧪 测试多次快速刷新...")
    
    tester = QuickRefreshTester()
    
    success_count = 0
    total_tests = 5
    
    for i in range(total_tests):
        print(f"\n--- 第 {i+1} 次测试 ---")
        if tester.quick_refresh_account():
            success_count += 1
        
        # 等待一段时间再进行下次测试
        if i < total_tests - 1:
            print("⏳ 等待3秒...")
            time.sleep(3)
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 次成功")
    return success_count == total_tests

def test_concurrent_refreshes():
    """测试并发刷新"""
    print("\n🔄 测试并发刷新...")
    
    tester = QuickRefreshTester()
    
    # 重置状态
    tester.is_account_updating = False
    tester.last_account_update_time = 0
    
    # 并发测试
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = []
        for i in range(3):
            future = executor.submit(tester.quick_refresh_account)
            futures.append(future)
        
        results = []
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print(f"❌ 并发测试异常: {str(e)}")
                results.append(False)
    
    success_count = sum(results)
    print(f"📊 并发测试结果: {success_count}/3 次成功")
    
    # 至少应该有一次成功（其他的应该被跳过）
    return success_count >= 1

def main():
    """主测试函数"""
    print("🚀 开始快速刷新功能测试...\n")
    
    # 测试多次刷新
    if not test_multiple_refreshes():
        print("❌ 多次刷新测试失败")
        return False
    
    # 测试并发刷新
    if not test_concurrent_refreshes():
        print("❌ 并发刷新测试失败")
        return False
    
    print("\n✅ 所有快速刷新测试通过！")
    print("\n💡 如果主程序仍然出现问题，可能的原因：")
    print("   1. UI线程和工作线程之间的信号连接问题")
    print("   2. 缓存机制的实现问题")
    print("   3. 状态指示器的更新问题")
    print("   4. 线程池的配置问题")
    
    return True

if __name__ == "__main__":
    main()
